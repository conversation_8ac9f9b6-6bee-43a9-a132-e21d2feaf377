<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1350</width>
    <height>717</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <widget class="QWidget" name="centralwidget">
   <property name="sizePolicy">
    <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true"/>
   </property>
   <layout class="QGridLayout" name="gridLayout_4">
    <item row="0" column="1" colspan="2">
     <layout class="QHBoxLayout" name="horizontalLayout_14">
      <item>
       <widget class="QWidget" name="widget_2" native="true">
        <property name="styleSheet">
         <string notr="true">background-color: transparent;</string>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_10">
         <property name="spacing">
          <number>0</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_11">
           <item>
            <spacer name="horizontalSpacer_11">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_9">
             <property name="spacing">
              <number>20</number>
             </property>
             <item>
              <widget class="QLabel" name="label_2">
               <property name="font">
                <font>
                 <pointsize>14</pointsize>
                </font>
               </property>
               <property name="text">
                <string>通道：UVC</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="label">
               <property name="font">
                <font>
                 <pointsize>14</pointsize>
                </font>
               </property>
               <property name="text">
                <string>分辨率：4K</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="label_3">
               <property name="font">
                <font>
                 <pointsize>14</pointsize>
                </font>
               </property>
               <property name="text">
                <string>帧率：60</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="label_4">
               <property name="font">
                <font>
                 <pointsize>14</pointsize>
                </font>
               </property>
               <property name="text">
                <string>编码：H264</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="label_5">
               <property name="font">
                <font>
                 <pointsize>14</pointsize>
                </font>
               </property>
               <property name="text">
                <string>码流：40MB</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="label_6">
               <property name="font">
                <font>
                 <pointsize>14</pointsize>
                </font>
               </property>
               <property name="text">
                <string>存储位置：TF卡</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <spacer name="horizontalSpacer_12">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QWidget" name="recordingIndicatorWidget" native="true">
        <property name="styleSheet">
         <string notr="true">background-color: transparent;</string>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_13">
         <property name="spacing">
          <number>0</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_12">
           <item>
            <spacer name="horizontalSpacer_recording">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>37</width>
               <height>17</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QLabel" name="recordingDotLabel">
             <property name="minimumSize">
              <size>
               <width>20</width>
               <height>20</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>20</width>
               <height>20</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">background-color: red;
border-radius: 10px;</string>
             </property>
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="recordingTimeLabel">
             <property name="font">
              <font>
               <pointsize>16</pointsize>
               <bold>true</bold>
              </font>
             </property>
             <property name="styleSheet">
              <string notr="true">color: rgb(255, 255, 255);
background-color: transparent;</string>
             </property>
             <property name="text">
              <string>00:00:00</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </item>
    <item row="1" column="2">
     <spacer name="verticalSpacer">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>85</height>
       </size>
      </property>
     </spacer>
    </item>
    <item row="2" column="0">
     <spacer name="horizontalSpacer_5">
      <property name="orientation">
       <enum>Qt::Horizontal</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>170</width>
        <height>20</height>
       </size>
      </property>
     </spacer>
    </item>
    <item row="2" column="1" rowspan="2" colspan="2">
     <layout class="QVBoxLayout" name="verticalLayout_3">
      <property name="spacing">
       <number>0</number>
      </property>
      <item>
       <widget class="QStackedWidget" name="stackedWidget">
        <property name="styleSheet">
         <string notr="true"/>
        </property>
        <property name="currentIndex">
         <number>0</number>
        </property>
        <widget class="QWidget" name="menu">
         <property name="styleSheet">
          <string notr="true">background-color: rgba(119, 118, 123,127);</string>
         </property>
         <layout class="QGridLayout" name="gridLayout_3">
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <property name="spacing">
           <number>0</number>
          </property>
          <item row="0" column="0">
           <layout class="QVBoxLayout" name="verticalLayout_4">
            <property name="spacing">
             <number>10</number>
            </property>
            <property name="bottomMargin">
             <number>10</number>
            </property>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_3">
              <item>
               <widget class="QLabel" name="man_menu">
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>50</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>31</pointsize>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">color: rgb(255, 255, 255);
background-color: rgba(119, 118, 123,0);</string>
                </property>
                <property name="text">
                 <string>主菜单</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_3">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <widget class="Line" name="line">
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>0</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">color: rgb(119, 118, 123);</string>
              </property>
              <property name="midLineWidth">
               <number>5</number>
              </property>
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
             </widget>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_2">
              <item>
               <layout class="QVBoxLayout" name="verticalLayout_2">
                <item>
                 <widget class="QPushButton" name="camera">
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <pointsize>31</pointsize>
                   </font>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
	
	background-color: rgba(255, 255, 255,127);
padding-left: 10px;
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;

text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                  </property>
                  <property name="text">
                   <string>图像参数</string>
                  </property>
                  <property name="checkable">
                   <bool>true</bool>
                  </property>
                  <property name="autoRepeat">
                   <bool>false</bool>
                  </property>
                  <property name="autoExclusive">
                   <bool>false</bool>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="custom">
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <pointsize>31</pointsize>
                   </font>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
	
	background-color: rgba(255, 255, 255,127);
padding-left: 10px;
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;

text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                  </property>
                  <property name="text">
                   <string>录像设置</string>
                  </property>
                  <property name="checkable">
                   <bool>true</bool>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="video_file">
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <pointsize>31</pointsize>
                   </font>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
	
	background-color: rgba(255, 255, 255,127);
padding-left: 10px;
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;

text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                  </property>
                  <property name="text">
                   <string>文件管理</string>
                  </property>
                  <property name="checkable">
                   <bool>true</bool>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="view_pictures">
                  <property name="minimumSize">
                   <size>
                    <width>220</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <pointsize>31</pointsize>
                   </font>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
	
	background-color: rgba(255, 255, 255,127);
padding-left: 10px;
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;

text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                  </property>
                  <property name="text">
                   <string>系统设置</string>
                  </property>
                  <property name="checkable">
                   <bool>true</bool>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
              <item>
               <spacer name="horizontalSpacer">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
        <widget class="QWidget" name="recording">
         <property name="styleSheet">
          <string notr="true">background-color: rgba(119, 118, 123,127);</string>
         </property>
         <layout class="QGridLayout" name="gridLayout_7">
          <item row="0" column="0">
           <layout class="QGridLayout" name="gridLayout_6">
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <property name="horizontalSpacing">
             <number>0</number>
            </property>
            <property name="verticalSpacing">
             <number>10</number>
            </property>
            <item row="0" column="0">
             <layout class="QHBoxLayout" name="horizontalLayout_6">
              <item>
               <widget class="QLabel" name="man_menu_3">
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>50</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>31</pointsize>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">color: rgb(255, 255, 255);
background-color: rgba(119, 118, 123,0);</string>
                </property>
                <property name="text">
                 <string>功能</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_8">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item row="5" column="0">
             <layout class="QHBoxLayout" name="horizontalLayout_8">
              <item>
               <widget class="QPushButton" name="start_photo">
                <property name="minimumSize">
                 <size>
                  <width>220</width>
                  <height>50</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>220</width>
                  <height>50</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>31</pointsize>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
background-color: rgba(255, 255, 255,127);
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;
text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                </property>
                <property name="text">
                 <string>拍照</string>
                </property>
                <property name="checkable">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_10">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeType">
                 <enum>QSizePolicy::Fixed</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item>
               <widget class="QLabel" name="is_photo">
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>50</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>31</pointsize>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">color: rgb(255, 255, 255);
background-color: rgba(119, 118, 123,0);
border:none;</string>
                </property>
                <property name="text">
                 <string>&lt; 是否拍照 &gt;</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignCenter</set>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item row="4" column="0">
             <layout class="QHBoxLayout" name="horizontalLayout_7">
              <item>
               <widget class="QPushButton" name="start_audio">
                <property name="minimumSize">
                 <size>
                  <width>220</width>
                  <height>50</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>220</width>
                  <height>50</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>31</pointsize>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
background-color: rgba(255, 255, 255,127);
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;
text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                </property>
                <property name="text">
                 <string>音频</string>
                </property>
                <property name="checkable">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_9">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeType">
                 <enum>QSizePolicy::Fixed</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item>
               <widget class="QLabel" name="is_audio">
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>50</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>31</pointsize>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">color: rgb(255, 255, 255);
background-color: rgba(119, 118, 123,0);
border:none;</string>
                </property>
                <property name="text">
                 <string>&lt; N &gt;</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignCenter</set>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item row="3" column="0">
             <layout class="QHBoxLayout" name="horizontalLayout_5">
              <item>
               <widget class="QPushButton" name="start_watermark">
                <property name="minimumSize">
                 <size>
                  <width>220</width>
                  <height>50</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>220</width>
                  <height>50</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>31</pointsize>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
background-color: rgba(255, 255, 255,127);
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;
text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                </property>
                <property name="text">
                 <string>水印</string>
                </property>
                <property name="checkable">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_7">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeType">
                 <enum>QSizePolicy::Fixed</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item>
               <widget class="QLabel" name="is_watermark">
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>50</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>31</pointsize>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">color: rgb(255, 255, 255);
background-color: rgba(119, 118, 123,0);
border:none;</string>
                </property>
                <property name="text">
                 <string>&lt; N &gt;</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignCenter</set>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item row="2" column="0">
             <layout class="QHBoxLayout" name="horizontalLayout_4">
              <item>
               <widget class="QPushButton" name="start_recording">
                <property name="minimumSize">
                 <size>
                  <width>220</width>
                  <height>50</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>220</width>
                  <height>50</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>31</pointsize>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
background-color: rgba(255, 255, 255,127);
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;
text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                </property>
                <property name="text">
                 <string>录像</string>
                </property>
                <property name="checkable">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_6">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeType">
                 <enum>QSizePolicy::Fixed</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item>
               <widget class="QLabel" name="is_record">
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>50</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>31</pointsize>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">color: rgb(255, 255, 255);
background-color: rgba(119, 118, 123,0);
border:none;</string>
                </property>
                <property name="text">
                 <string>&lt; N &gt;</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignCenter</set>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item row="1" column="0">
             <widget class="Line" name="line_3">
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>0</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">color: rgb(119, 118, 123);</string>
              </property>
              <property name="midLineWidth">
               <number>5</number>
              </property>
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
        <widget class="QWidget" name="videofile">
         <layout class="QGridLayout" name="gridLayout_8">
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <property name="spacing">
           <number>0</number>
          </property>
          <item row="0" column="0">
           <widget class="QListView" name="listView_videofile">
            <property name="styleSheet">
             <string notr="true">background-color: rgba(119, 118, 123,127);</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
        <widget class="QWidget" name="viewpictures">
         <layout class="QGridLayout" name="gridLayout_9">
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <property name="spacing">
           <number>0</number>
          </property>
          <item row="0" column="0">
           <widget class="QListView" name="listView_viewpictures">
            <property name="styleSheet">
             <string notr="true">background-color: rgba(119, 118, 123,127);</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </widget>
      </item>
      <item>
       <widget class="QWidget" name="widget" native="true">
        <property name="styleSheet">
         <string notr="true">background-color: rgba(119, 118, 123,127);</string>
        </property>
        <layout class="QGridLayout" name="gridLayout_2">
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <property name="spacing">
          <number>0</number>
         </property>
         <item row="0" column="0">
          <layout class="QVBoxLayout" name="verticalLayout">
           <item>
            <widget class="Line" name="line_2">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">color: rgb(119, 118, 123);</string>
             </property>
             <property name="midLineWidth">
              <number>5</number>
             </property>
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
            </widget>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout">
             <item>
              <layout class="QGridLayout" name="gridLayout">
               <item row="1" column="1">
                <widget class="QLabel" name="Return">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>50</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>20</pointsize>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">color: rgb(255, 255, 255);
background-color: rgba(119, 118, 123,0);</string>
                 </property>
                 <property name="text">
                  <string>[Menu]返回</string>
                 </property>
                </widget>
               </item>
               <item row="0" column="0">
                <widget class="QLabel" name="select">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>50</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>20</pointsize>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">color: rgb(255, 255, 255);
background-color: rgba(119, 118, 123,0);</string>
                 </property>
                 <property name="text">
                  <string>[⬆ ⬇]选择</string>
                 </property>
                </widget>
               </item>
               <item row="1" column="0">
                <widget class="QLabel" name="reset">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>50</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>20</pointsize>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">color: rgb(255, 255, 255);
background-color: rgba(119, 118, 123,0);</string>
                 </property>
                 <property name="text">
                  <string>[Reset]确定</string>
                 </property>
                </widget>
               </item>
               <item row="0" column="1">
                <widget class="QLabel" name="modification">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>50</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>20</pointsize>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">color: rgb(255, 255, 255);
background-color: rgba(119, 118, 123,0);</string>
                 </property>
                 <property name="text">
                  <string>[⬅ ➡]修改</string>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
             <item>
              <spacer name="horizontalSpacer_2">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>582</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </item>
          </layout>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </item>
    <item row="3" column="3">
     <spacer name="horizontalSpacer_4">
      <property name="orientation">
       <enum>Qt::Horizontal</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>169</width>
        <height>20</height>
       </size>
      </property>
     </spacer>
    </item>
    <item row="4" column="1">
     <spacer name="verticalSpacer_2">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>85</height>
       </size>
      </property>
     </spacer>
    </item>
   </layout>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
